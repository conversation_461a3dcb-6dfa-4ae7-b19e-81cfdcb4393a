#!/usr/bin/env python3
"""
日志文件转换工具主程序
支持将日志文件转换为TXT、Word、PDF格式
"""
import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main_gui import MainGUI
    from error_handler import setup_global_error_handling
    from logger_system import ConversionLogger
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有必需的文件都在同一目录下")
    sys.exit(1)


def check_dependencies():
    """检查依赖包是否已安装"""
    missing_packages = []
    
    try:
        import chardet
    except ImportError:
        missing_packages.append("chardet")
    
    try:
        import docx
    except ImportError:
        missing_packages.append("python-docx")
    
    try:
        import reportlab
    except ImportError:
        missing_packages.append("reportlab")
    
    if missing_packages:
        error_msg = (
            "缺少必需的依赖包:\n\n" +
            "\n".join(f"• {pkg}" for pkg in missing_packages) +
            "\n\n请运行以下命令安装:\n" +
            f"pip install {' '.join(missing_packages)}\n\n" +
            "或者运行:\n" +
            "pip install -r requirements.txt"
        )
        
        # 尝试显示GUI错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("依赖包缺失", error_msg)
            root.destroy()
        except:
            # 如果GUI不可用，输出到控制台
            print("错误: " + error_msg.replace("\n\n", "\n").replace("• ", "- "))
        
        return False
    
    return True


def main():
    """主函数"""
    print("日志文件转换工具 v1.0")
    print("正在启动...")
    
    # 检查依赖包
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # 创建日志记录器
        logger = ConversionLogger()
        
        # 设置全局错误处理
        setup_global_error_handling(logger)
        
        # 记录启动信息
        logger.log_info("日志文件转换工具启动")
        
        # 创建并运行GUI
        app = MainGUI()
        
        print("GUI界面已启动")
        logger.log_info("GUI界面已启动")
        
        # 运行主循环
        app.run()
        
        # 记录关闭信息
        logger.log_info("日志文件转换工具关闭")
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        error_msg = f"程序启动失败: {e}"
        print(f"错误: {error_msg}")
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
