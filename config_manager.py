"""
配置管理模块
负责保存和加载应用程序配置
"""
import json
import os
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.default_config = {
            "output_directory": "",
            "log_directory": "logs",
            "last_selected_directory": "",
            "default_format": "txt",
            "window_geometry": "800x600+100+100",
            "auto_create_directories": True
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            else:
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default=None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value
    
    def update(self, updates: Dict[str, Any]) -> None:
        """
        批量更新配置
        
        Args:
            updates: 要更新的配置字典
        """
        self.config.update(updates)
    
    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        if self.config.get("auto_create_directories", True):
            # 创建日志目录
            log_dir = self.config.get("log_directory", "logs")
            if log_dir:
                Path(log_dir).mkdir(parents=True, exist_ok=True)
            
            # 创建输出目录（如果已设置）
            output_dir = self.config.get("output_directory", "")
            if output_dir:
                Path(output_dir).mkdir(parents=True, exist_ok=True)
