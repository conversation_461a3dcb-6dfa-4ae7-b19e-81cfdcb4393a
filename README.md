# 日志文件转换工具

一个功能强大的Windows GUI应用程序，用于将各种格式的日志文件批量转换为TXT、Word或PDF格式。

## 功能特点

- **多格式支持**: 支持转换为TXT、Word (DOCX)、PDF格式
- **智能文件识别**: 自动识别各种扩展名的日志文件（.log、无扩展名、数字扩展名等）
- **批量处理**: 一次性转换整个目录下的所有日志文件
- **详细日志记录**: 记录转换操作的详细信息，包括文件大小、转换时间等
- **可配置路径**: 支持自定义输出目录和日志目录
- **进度显示**: 实时显示转换进度和当前处理的文件
- **错误处理**: 完善的错误处理和异常恢复机制
- **用户友好**: 直观的GUI界面，易于使用

## 系统要求

- Windows 11 (或Windows 10)
- Python 3.7 或更高版本
- 必需的Python包（见requirements.txt）

## 安装方法

### 方法一：使用安装脚本（推荐）

1. 下载所有文件到一个目录
2. 双击运行 `install.bat`
3. 等待依赖包安装完成

### 方法二：手动安装

1. 确保已安装Python 3.7+
2. 在命令行中运行：
   ```bash
   pip install -r requirements.txt
   ```
   或者：
   ```bash
   pip install chardet python-docx reportlab
   ```

## 使用方法

1. **启动程序**：
   - 双击 `log_converter.py` 文件
   - 或在命令行中运行：`python log_converter.py`

2. **选择源目录**：
   - 点击"浏览"按钮选择包含日志文件的目录
   - 程序会自动扫描该目录下的所有日志文件（不包括子目录）

3. **选择转换格式**：
   - TXT：纯文本格式
   - Word (DOCX)：Microsoft Word文档格式
   - PDF：便携式文档格式

4. **配置设置**（可选）：
   - 点击菜单栏"设置" → "配置"
   - 设置输出目录（留空则使用源目录）
   - 设置日志目录
   - 选择默认转换格式

5. **开始转换**：
   - 点击"开始转换"按钮
   - 程序会显示转换进度和当前处理的文件
   - 转换完成后会显示结果统计

## 文件说明

- `log_converter.py` - 主程序入口
- `main_gui.py` - 主GUI界面
- `config_dialog.py` - 配置对话框
- `file_converter.py` - 文件转换核心功能
- `logger_system.py` - 日志记录系统
- `config_manager.py` - 配置管理
- `error_handler.py` - 错误处理和稳定性保障
- `requirements.txt` - 依赖包列表
- `install.bat` - 安装脚本

## 配置文件

程序会在运行目录下创建 `config.json` 配置文件，保存用户的设置：

```json
{
    "output_directory": "",
    "log_directory": "logs",
    "last_selected_directory": "",
    "default_format": "txt",
    "window_geometry": "800x600+100+100",
    "auto_create_directories": true
}
```

## 日志文件

转换日志保存在 `logs` 目录下，按日期命名：
- `conversion_2024-01-01.log` - 转换操作日志

日志内容包括：
- 转换开始/完成时间
- 源文件和目标文件路径
- 文件大小信息
- 转换耗时
- 错误信息（如有）

## 支持的文件类型

程序会自动识别以下类型的日志文件：
- `.log` 扩展名的文件
- 无扩展名的文本文件
- 数字扩展名的文件（如 `.1`, `.2` 等）
- 其他可读取的文本文件

程序会自动跳过二进制文件和无法读取的文件。

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python是否正确安装
   - 运行 `install.bat` 安装依赖包

2. **转换失败**
   - 检查源文件是否可读
   - 确保有足够的磁盘空间
   - 查看日志文件了解详细错误信息

3. **PDF转换中文显示问题**
   - 程序会自动尝试使用系统中文字体
   - 如果中文显示异常，请确保系统已安装中文字体

4. **内存不足**
   - 对于大文件，程序会分段处理
   - 如果仍有问题，可以分批转换文件

### 获取帮助

如果遇到问题：
1. 查看程序日志文件
2. 检查错误信息
3. 确保所有依赖包已正确安装

## 版本信息

- 版本：1.0.0
- 开发者：AI Assistant
- 支持的Python版本：3.7+
- 支持的操作系统：Windows 10/11
