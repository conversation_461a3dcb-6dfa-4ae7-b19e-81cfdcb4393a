@echo off
chcp 65001 >nul
echo 日志文件转换工具 - 安装脚本
echo ================================

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 依赖包安装失败，请检查网络连接或手动安装
    echo 手动安装命令:
    echo pip install chardet python-docx reportlab
    pause
    exit /b 1
)

echo.
echo 安装完成！
echo.
echo 使用方法:
echo 1. 双击运行 log_converter.py
echo 2. 或在命令行中运行: python log_converter.py
echo.
pause
