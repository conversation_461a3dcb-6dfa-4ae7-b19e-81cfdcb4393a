"""
日志系统模块
负责记录文件转换操作的详细信息
"""
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Optional


class ConversionLogger:
    """文件转换日志记录器"""
    
    def __init__(self, log_directory: str = "logs"):
        """
        初始化日志记录器
        
        Args:
            log_directory: 日志文件存储目录
        """
        self.log_directory = log_directory
        self.setup_logger()
    
    def setup_logger(self) -> None:
        """设置日志记录器"""
        # 确保日志目录存在
        Path(self.log_directory).mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件名（按日期）
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = os.path.join(self.log_directory, f"conversion_{today}.log")
        
        # 配置日志格式
        log_format = "%(asctime)s - %(levelname)s - %(message)s"
        date_format = "%Y-%m-%d %H:%M:%S"
        
        # 创建logger
        self.logger = logging.getLogger("FileConverter")
        self.logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter(log_format, date_format)
        file_handler.setFormatter(file_formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(log_format, date_format)
        console_handler.setFormatter(console_formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def log_conversion_start(self, source_file: str, target_format: str) -> None:
        """
        记录转换开始
        
        Args:
            source_file: 源文件路径
            target_format: 目标格式
        """
        self.logger.info(f"开始转换: {source_file} -> {target_format}")
    
    def log_conversion_success(self, source_file: str, target_file: str, 
                             source_size: int, target_size: int, 
                             duration: float) -> None:
        """
        记录转换成功
        
        Args:
            source_file: 源文件路径
            target_file: 目标文件路径
            source_size: 源文件大小（字节）
            target_size: 目标文件大小（字节）
            duration: 转换耗时（秒）
        """
        source_size_mb = source_size / (1024 * 1024)
        target_size_mb = target_size / (1024 * 1024)
        
        self.logger.info(
            f"转换成功: {source_file} -> {target_file} | "
            f"源文件大小: {source_size_mb:.2f}MB | "
            f"目标文件大小: {target_size_mb:.2f}MB | "
            f"耗时: {duration:.2f}秒"
        )
    
    def log_conversion_error(self, source_file: str, target_format: str, 
                           error: str) -> None:
        """
        记录转换错误
        
        Args:
            source_file: 源文件路径
            target_format: 目标格式
            error: 错误信息
        """
        self.logger.error(
            f"转换失败: {source_file} -> {target_format} | 错误: {error}"
        )
    
    def log_batch_start(self, total_files: int, target_format: str) -> None:
        """
        记录批量转换开始
        
        Args:
            total_files: 总文件数
            target_format: 目标格式
        """
        self.logger.info(f"开始批量转换: 共{total_files}个文件 -> {target_format}")
    
    def log_batch_complete(self, total_files: int, success_count: int, 
                          error_count: int, total_duration: float) -> None:
        """
        记录批量转换完成
        
        Args:
            total_files: 总文件数
            success_count: 成功转换数
            error_count: 失败转换数
            total_duration: 总耗时（秒）
        """
        self.logger.info(
            f"批量转换完成: 总计{total_files}个文件 | "
            f"成功{success_count}个 | 失败{error_count}个 | "
            f"总耗时: {total_duration:.2f}秒"
        )
    
    def log_info(self, message: str) -> None:
        """记录信息"""
        self.logger.info(message)
    
    def log_warning(self, message: str) -> None:
        """记录警告"""
        self.logger.warning(message)
    
    def log_error(self, message: str) -> None:
        """记录错误"""
        self.logger.error(message)
    
    def get_log_file_path(self) -> str:
        """获取当前日志文件路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        return os.path.join(self.log_directory, f"conversion_{today}.log")
