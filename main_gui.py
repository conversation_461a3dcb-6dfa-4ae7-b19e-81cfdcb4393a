"""
主GUI界面模块
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
from pathlib import Path

from config_manager import ConfigManager
from logger_system import ConversionLogger
from file_converter import FileConverter


class MainGUI:
    """主GUI界面"""
    
    def __init__(self):
        """初始化主界面"""
        self.root = tk.Tk()
        self.root.title("日志文件转换工具")
        self.root.geometry("800x600")
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.config_manager.ensure_directories()
        
        self.logger = ConversionLogger(self.config_manager.get("log_directory", "logs"))
        self.converter = FileConverter(self.logger)
        
        # 界面变量
        self.source_directory = tk.StringVar()
        self.target_format = tk.StringVar(value="txt")
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        self.current_file_var = tk.StringVar()
        
        # 转换状态
        self.is_converting = False
        
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建菜单
        self.create_menu()
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 源目录选择
        ttk.Label(main_frame, text="源目录:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        dir_frame = ttk.Frame(main_frame)
        dir_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        dir_frame.columnconfigure(0, weight=1)
        
        self.dir_entry = ttk.Entry(dir_frame, textvariable=self.source_directory, width=50)
        self.dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(dir_frame, text="浏览", command=self.browse_directory).grid(row=0, column=1)
        
        # 目标格式选择
        ttk.Label(main_frame, text="目标格式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        format_frame = ttk.Frame(main_frame)
        format_frame.grid(row=1, column=1, sticky=tk.W, pady=5)
        
        ttk.Radiobutton(format_frame, text="TXT", variable=self.target_format, 
                       value="txt").grid(row=0, column=0, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="Word (DOCX)", variable=self.target_format, 
                       value="docx").grid(row=0, column=1, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="PDF", variable=self.target_format, 
                       value="pdf").grid(row=0, column=2)
        
        # 输出目录显示
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.output_label = ttk.Label(main_frame, text="", foreground="blue")
        self.output_label.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 转换按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                       command=self.start_conversion, style="Accent.TButton")
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止转换", 
                                    command=self.stop_conversion, state="disabled")
        self.stop_button.pack(side=tk.LEFT)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="转换进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W, pady=2)
        
        self.current_file_label = ttk.Label(progress_frame, textvariable=self.current_file_var, 
                                          foreground="gray")
        self.current_file_label.grid(row=2, column=0, sticky=tk.W, pady=2)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="转换日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(text_frame, height=10, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        log_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        log_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.grid(row=1, column=0, pady=5)
        
        ttk.Button(log_button_frame, text="清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_button_frame, text="保存日志", 
                  command=self.save_log).pack(side=tk.LEFT)
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        settings_menu.add_command(label="配置", command=self.open_config_dialog)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)

    def load_settings(self):
        """加载设置"""
        # 加载窗口几何信息
        geometry = self.config_manager.get("window_geometry", "800x600+100+100")
        self.root.geometry(geometry)

        # 加载上次选择的目录
        last_dir = self.config_manager.get("last_selected_directory", "")
        if last_dir and os.path.exists(last_dir):
            self.source_directory.set(last_dir)

        # 加载默认格式
        default_format = self.config_manager.get("default_format", "txt")
        self.target_format.set(default_format)

        # 更新输出目录显示
        self.update_output_directory_display()

    def save_settings(self):
        """保存设置"""
        # 保存窗口几何信息
        self.config_manager.set("window_geometry", self.root.geometry())

        # 保存当前选择的目录
        if self.source_directory.get():
            self.config_manager.set("last_selected_directory", self.source_directory.get())

        # 保存默认格式
        self.config_manager.set("default_format", self.target_format.get())

        # 保存配置
        self.config_manager.save_config()

    def update_output_directory_display(self):
        """更新输出目录显示"""
        output_dir = self.config_manager.get("output_directory", "")
        if not output_dir:
            output_dir = "与源文件相同目录"
        self.output_label.config(text=output_dir)

    def browse_directory(self):
        """浏览选择目录"""
        directory = filedialog.askdirectory(
            title="选择包含日志文件的目录",
            initialdir=self.source_directory.get() or os.getcwd()
        )

        if directory:
            self.source_directory.set(directory)

    def start_conversion(self):
        """开始转换"""
        source_dir = self.source_directory.get().strip()
        if not source_dir:
            messagebox.showerror("错误", "请选择源目录")
            return

        if not os.path.exists(source_dir):
            messagebox.showerror("错误", "源目录不存在")
            return

        # 获取输出目录
        output_dir = self.config_manager.get("output_directory", "")
        if not output_dir:
            output_dir = source_dir  # 使用源目录作为输出目录

        # 检查是否有可转换的文件
        log_files = self.converter.get_log_files(source_dir)
        if not log_files:
            messagebox.showwarning("警告", "在选定目录中未找到可转换的日志文件")
            return

        # 确认转换
        result = messagebox.askyesno(
            "确认转换",
            f"找到 {len(log_files)} 个日志文件\n"
            f"将转换为 {self.target_format.get().upper()} 格式\n"
            f"输出目录: {output_dir}\n\n"
            f"是否开始转换？"
        )

        if not result:
            return

        # 开始转换
        self.is_converting = True
        self.convert_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # 清空日志显示
        self.log_text.delete(1.0, tk.END)

        # 在新线程中执行转换
        self.conversion_thread = threading.Thread(
            target=self.perform_conversion,
            args=(source_dir, self.target_format.get(), output_dir),
            daemon=True
        )
        self.conversion_thread.start()

    def stop_conversion(self):
        """停止转换"""
        self.is_converting = False
        self.status_var.set("正在停止...")

    def perform_conversion(self, source_dir, target_format, output_dir):
        """执行转换（在后台线程中运行）"""
        try:
            def progress_callback(current, total, filename):
                if not self.is_converting:
                    return

                progress = (current / total) * 100
                self.root.after(0, lambda: self.progress_var.set(progress))
                self.root.after(0, lambda: self.status_var.set(f"转换中... ({current}/{total})"))
                self.root.after(0, lambda: self.current_file_var.set(f"当前文件: {filename}"))
                self.root.after(0, lambda: self.append_log(f"正在转换: {filename}"))

            # 执行批量转换
            success_count, error_count, error_files = self.converter.batch_convert(
                source_dir, target_format, output_dir, progress_callback
            )

            # 更新UI
            if self.is_converting:
                self.root.after(0, lambda: self.conversion_complete(success_count, error_count, error_files))
            else:
                self.root.after(0, lambda: self.conversion_stopped())

        except Exception as e:
            self.root.after(0, lambda: self.conversion_error(str(e)))

    def conversion_complete(self, success_count, error_count, error_files):
        """转换完成"""
        self.is_converting = False
        self.convert_button.config(state="normal")
        self.stop_button.config(state="disabled")

        self.progress_var.set(100)
        self.status_var.set("转换完成")
        self.current_file_var.set("")

        # 显示结果
        message = f"转换完成！\n成功: {success_count} 个文件\n失败: {error_count} 个文件"
        if error_files:
            message += f"\n\n失败的文件:\n" + "\n".join([os.path.basename(f) for f in error_files[:5]])
            if len(error_files) > 5:
                message += f"\n... 还有 {len(error_files) - 5} 个文件"

        messagebox.showinfo("转换完成", message)
        self.append_log(f"批量转换完成: 成功 {success_count} 个，失败 {error_count} 个")

    def conversion_stopped(self):
        """转换被停止"""
        self.is_converting = False
        self.convert_button.config(state="normal")
        self.stop_button.config(state="disabled")

        self.status_var.set("转换已停止")
        self.current_file_var.set("")

        self.append_log("转换被用户停止")

    def conversion_error(self, error_message):
        """转换出错"""
        self.is_converting = False
        self.convert_button.config(state="normal")
        self.stop_button.config(state="disabled")

        self.status_var.set("转换出错")
        self.current_file_var.set("")

        messagebox.showerror("转换错误", f"转换过程中发生错误:\n{error_message}")
        self.append_log(f"转换错误: {error_message}")

    def append_log(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志显示"""
        self.log_text.delete(1.0, tk.END)

    def save_log(self):
        """保存日志到文件"""
        content = self.log_text.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("警告", "没有日志内容可保存")
            return

        filename = filedialog.asksaveasfilename(
            title="保存日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", "日志已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存日志失败: {e}")

    def open_config_dialog(self):
        """打开配置对话框"""
        from config_dialog import ConfigDialog
        dialog = ConfigDialog(self.root, self.config_manager)
        self.root.wait_window(dialog.dialog)

        # 更新输出目录显示
        self.update_output_directory_display()

        # 重新初始化日志系统（如果日志目录改变了）
        new_log_dir = self.config_manager.get("log_directory", "logs")
        if new_log_dir != self.logger.log_directory:
            self.logger = ConversionLogger(new_log_dir)
            self.converter.logger = self.logger

    def show_about(self):
        """显示关于对话框"""
        about_text = """日志文件转换工具 v1.0

功能特点:
• 支持转换为 TXT、Word、PDF 格式
• 自动识别各种扩展名的日志文件
• 批量转换处理
• 详细的转换日志记录
• 可配置的输出和日志路径

开发者: AI Assistant
版本: 1.0.0"""

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件"""
        if self.is_converting:
            result = messagebox.askyesno("确认退出", "转换正在进行中，确定要退出吗？")
            if not result:
                return

            self.is_converting = False

        # 保存设置
        self.save_settings()

        # 关闭窗口
        self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


if __name__ == "__main__":
    app = MainGUI()
    app.run()
