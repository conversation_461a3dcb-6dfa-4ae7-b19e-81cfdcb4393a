调试日志文件 - 数字扩展名
========================

DEBUG 2025-07-23 08:00:00 - 程序入口点
DEBUG 2025-07-23 08:00:01 - 解析命令行参数
DEBUG 2025-07-23 08:00:02 - 设置日志级别: DEBUG
DEBUG 2025-07-23 08:00:03 - 加载配置文件: config.json
DEBUG 2025-07-23 08:00:04 - 验证配置参数
DEBUG 2025-07-23 08:00:05 - 初始化数据库连接池
DEBUG 2025-07-23 08:00:06 - 创建工作线程池
DEBUG 2025-07-23 08:00:07 - 注册信号处理器
DEBUG 2025-07-23 08:00:08 - 启动HTTP服务器
DEBUG 2025-07-23 08:00:09 - 绑定端口: 8080
DEBUG 2025-07-23 08:00:10 - 服务器启动成功

DEBUG 2025-07-23 08:01:00 - 接收HTTP请求: GET /
DEBUG 2025-07-23 08:01:01 - 路由匹配: index
DEBUG 2025-07-23 08:01:02 - 执行控制器: IndexController
DEBUG 2025-07-23 08:01:03 - 渲染模板: index.html
DEBUG 2025-07-23 08:01:04 - 返回响应: 200 OK

DEBUG 2025-07-23 08:02:00 - 接收HTTP请求: POST /api/login
DEBUG 2025-07-23 08:02:01 - 解析请求体: JSON
DEBUG 2025-07-23 08:02:02 - 验证用户凭据
DEBUG 2025-07-23 08:02:03 - 查询用户数据库
DEBUG 2025-07-23 08:02:04 - 生成访问令牌
DEBUG 2025-07-23 08:02:05 - 返回响应: 200 OK
