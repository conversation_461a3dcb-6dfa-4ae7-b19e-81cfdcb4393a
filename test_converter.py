#!/usr/bin/env python3
"""
测试文件转换功能
"""
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from file_converter import FileConverter
from logger_system import ConversionLogger


def test_conversion():
    """测试转换功能"""
    print("开始测试文件转换功能...")
    
    # 创建日志记录器和转换器
    logger = ConversionLogger("test_logs")
    converter = FileConverter(logger)
    
    # 测试目录
    test_dir = "test_logs"
    output_dir = "test_output"
    
    # 确保输出目录存在
    Path(output_dir).mkdir(exist_ok=True)
    
    # 获取测试文件
    log_files = converter.get_log_files(test_dir)
    print(f"找到 {len(log_files)} 个日志文件:")
    for file in log_files:
        print(f"  - {file}")
    
    # 测试转换为不同格式
    formats = ['txt', 'docx', 'pdf']
    
    for fmt in formats:
        print(f"\n测试转换为 {fmt.upper()} 格式...")
        
        success_count, error_count, error_files = converter.batch_convert(
            test_dir, fmt, output_dir
        )
        
        print(f"转换结果: 成功 {success_count} 个，失败 {error_count} 个")
        
        if error_files:
            print("失败的文件:")
            for error_file in error_files:
                print(f"  - {error_file}")
    
    print("\n转换测试完成！")
    print(f"输出文件保存在: {output_dir}")
    
    # 列出输出文件
    if os.path.exists(output_dir):
        output_files = list(Path(output_dir).glob("*"))
        print(f"\n生成的文件 ({len(output_files)} 个):")
        for file in sorted(output_files):
            size = file.stat().st_size
            print(f"  - {file.name} ({size} 字节)")


if __name__ == "__main__":
    test_conversion()
