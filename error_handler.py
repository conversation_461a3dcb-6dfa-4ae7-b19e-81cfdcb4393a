"""
错误处理和稳定性保障模块
"""
import functools
import traceback
import sys
from typing import Callable, Any
import tkinter as tk
from tkinter import messagebox


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, logger=None):
        """
        初始化错误处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """
        全局异常处理器
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许 Ctrl+C 中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录异常
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        if self.logger:
            self.logger.log_error(f"未处理的异常: {error_msg}")
        
        # 显示错误对话框
        try:
            messagebox.showerror(
                "程序错误",
                f"程序发生未处理的错误:\n\n{exc_type.__name__}: {exc_value}\n\n"
                f"请查看日志文件获取详细信息。"
            )
        except:
            # 如果连错误对话框都无法显示，则输出到控制台
            print(f"Critical error: {error_msg}")
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        安全执行函数，捕获并处理异常
        
        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            函数执行结果，如果出错则返回None
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"执行函数 {func.__name__} 时发生错误: {e}"
            
            if self.logger:
                self.logger.log_error(error_msg)
            
            # 显示错误信息
            try:
                messagebox.showerror("执行错误", error_msg)
            except:
                print(error_msg)
            
            return None


def safe_method(logger=None):
    """
    装饰器：为方法添加异常处理
    
    Args:
        logger: 日志记录器
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_msg = f"方法 {func.__name__} 执行失败: {e}"
                
                if logger:
                    logger.log_error(error_msg)
                else:
                    print(error_msg)
                
                # 如果是GUI方法，显示错误对话框
                try:
                    messagebox.showerror("方法执行错误", error_msg)
                except:
                    pass
                
                return None
        return wrapper
    return decorator


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_items: int, callback=None):
        """
        初始化进度跟踪器
        
        Args:
            total_items: 总项目数
            callback: 进度回调函数 (current, total, percentage)
        """
        self.total_items = total_items
        self.current_item = 0
        self.callback = callback
        self.is_cancelled = False
    
    def update(self, increment: int = 1, item_name: str = ""):
        """
        更新进度
        
        Args:
            increment: 增量
            item_name: 当前项目名称
        """
        if self.is_cancelled:
            return False
        
        self.current_item += increment
        
        if self.current_item > self.total_items:
            self.current_item = self.total_items
        
        percentage = (self.current_item / self.total_items) * 100 if self.total_items > 0 else 0
        
        if self.callback:
            try:
                self.callback(self.current_item, self.total_items, percentage, item_name)
            except Exception as e:
                print(f"进度回调函数执行失败: {e}")
        
        return not self.is_cancelled
    
    def cancel(self):
        """取消操作"""
        self.is_cancelled = True
    
    def is_complete(self) -> bool:
        """检查是否完成"""
        return self.current_item >= self.total_items
    
    def reset(self):
        """重置进度"""
        self.current_item = 0
        self.is_cancelled = False


class ResourceManager:
    """资源管理器"""
    
    def __init__(self):
        """初始化资源管理器"""
        self.resources = []
    
    def add_resource(self, resource):
        """添加资源"""
        self.resources.append(resource)
    
    def cleanup(self):
        """清理所有资源"""
        for resource in self.resources:
            try:
                if hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'cleanup'):
                    resource.cleanup()
            except Exception as e:
                print(f"清理资源时发生错误: {e}")
        
        self.resources.clear()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()


class RetryHandler:
    """重试处理器"""
    
    @staticmethod
    def retry(max_attempts: int = 3, delay: float = 1.0, exceptions=(Exception,)):
        """
        重试装饰器
        
        Args:
            max_attempts: 最大重试次数
            delay: 重试间隔（秒）
            exceptions: 需要重试的异常类型
        """
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                import time
                
                last_exception = None
                
                for attempt in range(max_attempts):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        if attempt < max_attempts - 1:
                            time.sleep(delay)
                            continue
                        else:
                            raise last_exception
                
                return None
            return wrapper
        return decorator


def setup_global_error_handling(logger=None):
    """
    设置全局错误处理
    
    Args:
        logger: 日志记录器
    """
    error_handler = ErrorHandler(logger)
    sys.excepthook = error_handler.handle_exception
    
    # 设置tkinter的错误处理
    def tk_error_handler(exc, val, tb):
        error_handler.handle_exception(exc, val, tb)
    
    tk.Tk.report_callback_exception = tk_error_handler
