"""
文件转换核心模块
负责将日志文件转换为不同格式
"""
import os
import time
from pathlib import Path
from typing import List, Tuple, Optional
import chardet
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from docx import Document
from docx.shared import Inches


class FileConverter:
    """文件转换器"""
    
    def __init__(self, logger=None):
        """
        初始化文件转换器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger
        self.supported_formats = ['txt', 'docx', 'pdf']
        self.setup_pdf_fonts()
    
    def setup_pdf_fonts(self) -> None:
        """设置PDF中文字体支持"""
        try:
            # 尝试注册中文字体
            font_paths = [
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont('ChineseFont', font_path))
                        self.chinese_font = 'ChineseFont'
                        break
                    except:
                        continue
            else:
                self.chinese_font = 'Helvetica'  # 备用字体
        except Exception as e:
            self.chinese_font = 'Helvetica'
            if self.logger:
                self.logger.log_warning(f"PDF字体设置失败: {e}")
    
    def detect_encoding(self, file_path: str) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            检测到的编码
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # 读取前10KB用于检测
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                
                # 常见编码映射
                encoding_map = {
                    'GB2312': 'gbk',
                    'GBK': 'gbk',
                    'UTF-8-SIG': 'utf-8-sig'
                }
                
                return encoding_map.get(encoding, encoding or 'utf-8')
        except Exception:
            return 'utf-8'
    
    def read_file_content(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        encoding = self.detect_encoding(file_path)
        
        # 尝试多种编码
        encodings = [encoding, 'utf-8', 'gbk', 'gb2312', 'latin-1']
        
        for enc in encodings:
            try:
                with open(file_path, 'r', encoding=enc, errors='ignore') as f:
                    return f.read()
            except Exception:
                continue
        
        # 如果所有编码都失败，使用二进制模式读取
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return content.decode('utf-8', errors='ignore')
        except Exception as e:
            raise Exception(f"无法读取文件内容: {e}")
    
    def is_log_file(self, file_path: str) -> bool:
        """
        判断是否为日志文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为日志文件
        """
        try:
            # 检查文件是否可读且不是二进制文件
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                if b'\x00' in chunk:  # 包含空字节，可能是二进制文件
                    return False
            
            # 尝试读取文件内容
            content = self.read_file_content(file_path)
            return len(content.strip()) > 0
            
        except Exception:
            return False
    
    def get_log_files(self, directory: str) -> List[str]:
        """
        获取目录下的所有日志文件
        
        Args:
            directory: 目录路径
            
        Returns:
            日志文件路径列表
        """
        log_files = []
        
        try:
            for item in os.listdir(directory):
                file_path = os.path.join(directory, item)
                
                # 只处理文件，不处理子目录
                if os.path.isfile(file_path):
                    if self.is_log_file(file_path):
                        log_files.append(file_path)
        except Exception as e:
            if self.logger:
                self.logger.log_error(f"扫描目录失败: {e}")
        
        return log_files

    def convert_to_txt(self, source_file: str, target_file: str) -> bool:
        """
        转换为TXT格式

        Args:
            source_file: 源文件路径
            target_file: 目标文件路径

        Returns:
            是否转换成功
        """
        try:
            content = self.read_file_content(source_file)

            with open(target_file, 'w', encoding='utf-8') as f:
                f.write(content)

            return True
        except Exception as e:
            if self.logger:
                self.logger.log_error(f"转换为TXT失败: {e}")
            return False

    def convert_to_docx(self, source_file: str, target_file: str) -> bool:
        """
        转换为DOCX格式

        Args:
            source_file: 源文件路径
            target_file: 目标文件路径

        Returns:
            是否转换成功
        """
        try:
            content = self.read_file_content(source_file)

            doc = Document()

            # 添加标题
            doc.add_heading(f'日志文件: {os.path.basename(source_file)}', 0)

            # 分段添加内容
            lines = content.split('\n')
            current_paragraph = ""

            for line in lines:
                if len(current_paragraph) + len(line) > 1000:  # 避免段落过长
                    if current_paragraph.strip():
                        doc.add_paragraph(current_paragraph.strip())
                    current_paragraph = line
                else:
                    current_paragraph += line + '\n'

            # 添加最后一段
            if current_paragraph.strip():
                doc.add_paragraph(current_paragraph.strip())

            doc.save(target_file)
            return True

        except Exception as e:
            if self.logger:
                self.logger.log_error(f"转换为DOCX失败: {e}")
            return False

    def convert_to_pdf(self, source_file: str, target_file: str) -> bool:
        """
        转换为PDF格式

        Args:
            source_file: 源文件路径
            target_file: 目标文件路径

        Returns:
            是否转换成功
        """
        try:
            content = self.read_file_content(source_file)

            doc = SimpleDocTemplate(target_file, pagesize=letter)
            styles = getSampleStyleSheet()

            # 创建支持中文的样式
            chinese_style = ParagraphStyle(
                'ChineseStyle',
                parent=styles['Normal'],
                fontName=self.chinese_font,
                fontSize=10,
                leading=12,
                wordWrap='CJK'
            )

            title_style = ParagraphStyle(
                'ChineseTitleStyle',
                parent=styles['Title'],
                fontName=self.chinese_font,
                fontSize=16,
                leading=20
            )

            story = []

            # 添加标题
            title = Paragraph(f'日志文件: {os.path.basename(source_file)}', title_style)
            story.append(title)
            story.append(Spacer(1, 0.2*inch))

            # 分段添加内容
            lines = content.split('\n')
            current_paragraph = ""

            for line in lines:
                # 处理特殊字符
                line = line.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

                if len(current_paragraph) + len(line) > 2000:  # 避免段落过长
                    if current_paragraph.strip():
                        para = Paragraph(current_paragraph.strip(), chinese_style)
                        story.append(para)
                        story.append(Spacer(1, 0.1*inch))
                    current_paragraph = line
                else:
                    current_paragraph += line + '<br/>'

            # 添加最后一段
            if current_paragraph.strip():
                para = Paragraph(current_paragraph.strip(), chinese_style)
                story.append(para)

            doc.build(story)
            return True

        except Exception as e:
            if self.logger:
                self.logger.log_error(f"转换为PDF失败: {e}")
            return False

    def convert_file(self, source_file: str, target_format: str, output_directory: str) -> Tuple[bool, str]:
        """
        转换单个文件

        Args:
            source_file: 源文件路径
            target_format: 目标格式 (txt, docx, pdf)
            output_directory: 输出目录

        Returns:
            (是否成功, 目标文件路径)
        """
        try:
            # 生成目标文件名
            source_name = Path(source_file).stem
            target_file = os.path.join(output_directory, f"{source_name}.{target_format}")

            # 确保输出目录存在
            Path(output_directory).mkdir(parents=True, exist_ok=True)

            # 记录开始时间
            start_time = time.time()

            if self.logger:
                self.logger.log_conversion_start(source_file, target_format)

            # 根据格式调用相应的转换方法
            success = False
            if target_format.lower() == 'txt':
                success = self.convert_to_txt(source_file, target_file)
            elif target_format.lower() == 'docx':
                success = self.convert_to_docx(source_file, target_file)
            elif target_format.lower() == 'pdf':
                success = self.convert_to_pdf(source_file, target_file)
            else:
                raise ValueError(f"不支持的格式: {target_format}")

            if success:
                # 记录转换成功
                end_time = time.time()
                duration = end_time - start_time

                source_size = os.path.getsize(source_file)
                target_size = os.path.getsize(target_file)

                if self.logger:
                    self.logger.log_conversion_success(
                        source_file, target_file, source_size, target_size, duration
                    )

                return True, target_file
            else:
                return False, ""

        except Exception as e:
            if self.logger:
                self.logger.log_conversion_error(source_file, target_format, str(e))
            return False, ""

    def batch_convert(self, source_directory: str, target_format: str,
                     output_directory: str, progress_callback=None) -> Tuple[int, int, List[str]]:
        """
        批量转换文件

        Args:
            source_directory: 源目录
            target_format: 目标格式
            output_directory: 输出目录
            progress_callback: 进度回调函数 (current, total, filename)

        Returns:
            (成功数量, 失败数量, 错误文件列表)
        """
        start_time = time.time()

        # 获取所有日志文件
        log_files = self.get_log_files(source_directory)
        total_files = len(log_files)

        if total_files == 0:
            if self.logger:
                self.logger.log_warning("未找到可转换的日志文件")
            return 0, 0, []

        if self.logger:
            self.logger.log_batch_start(total_files, target_format)

        success_count = 0
        error_count = 0
        error_files = []

        for i, source_file in enumerate(log_files):
            try:
                # 更新进度
                if progress_callback:
                    progress_callback(i + 1, total_files, os.path.basename(source_file))

                # 转换文件
                success, target_file = self.convert_file(source_file, target_format, output_directory)

                if success:
                    success_count += 1
                else:
                    error_count += 1
                    error_files.append(source_file)

            except Exception as e:
                error_count += 1
                error_files.append(source_file)
                if self.logger:
                    self.logger.log_error(f"处理文件 {source_file} 时发生错误: {e}")

        # 记录批量转换完成
        end_time = time.time()
        total_duration = end_time - start_time

        if self.logger:
            self.logger.log_batch_complete(total_files, success_count, error_count, total_duration)

        return success_count, error_count, error_files
