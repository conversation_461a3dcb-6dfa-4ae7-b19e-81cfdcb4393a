"""
配置对话框模块
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os


class ConfigDialog:
    """配置对话框"""
    
    def __init__(self, parent, config_manager):
        """
        初始化配置对话框
        
        Args:
            parent: 父窗口
            config_manager: 配置管理器
        """
        self.parent = parent
        self.config_manager = config_manager
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("配置设置")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        
        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 配置变量
        self.output_directory = tk.StringVar()
        self.log_directory = tk.StringVar()
        self.auto_create_directories = tk.BooleanVar()
        self.default_format = tk.StringVar()
        
        # 加载当前配置
        self.load_current_config()
        
        # 创建界面
        self.setup_ui()
    
    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()
        
        # 获取窗口尺寸
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        
        # 获取屏幕尺寸
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")
    
    def load_current_config(self):
        """加载当前配置"""
        self.output_directory.set(self.config_manager.get("output_directory", ""))
        self.log_directory.set(self.config_manager.get("log_directory", "logs"))
        self.auto_create_directories.set(self.config_manager.get("auto_create_directories", True))
        self.default_format.set(self.config_manager.get("default_format", "txt"))
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="配置设置", font=("", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 输出目录设置
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(output_frame, text="输出目录:").pack(anchor=tk.W)
        
        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.output_entry = ttk.Entry(output_dir_frame, textvariable=self.output_directory, width=40)
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(output_dir_frame, text="浏览", 
                  command=self.browse_output_directory).pack(side=tk.RIGHT)
        
        ttk.Label(output_frame, text="留空则使用源文件所在目录", 
                 foreground="gray", font=("", 8)).pack(anchor=tk.W, pady=(2, 0))
        
        # 日志设置
        log_frame = ttk.LabelFrame(main_frame, text="日志设置", padding="10")
        log_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(log_frame, text="日志目录:").pack(anchor=tk.W)
        
        log_dir_frame = ttk.Frame(log_frame)
        log_dir_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.log_entry = ttk.Entry(log_dir_frame, textvariable=self.log_directory, width=40)
        self.log_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        ttk.Button(log_dir_frame, text="浏览", 
                  command=self.browse_log_directory).pack(side=tk.RIGHT)
        
        # 其他设置
        other_frame = ttk.LabelFrame(main_frame, text="其他设置", padding="10")
        other_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Checkbutton(other_frame, text="自动创建目录", 
                       variable=self.auto_create_directories).pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(other_frame, text="默认转换格式:").pack(anchor=tk.W)
        
        format_frame = ttk.Frame(other_frame)
        format_frame.pack(anchor=tk.W, pady=(5, 0))
        
        ttk.Radiobutton(format_frame, text="TXT", variable=self.default_format, 
                       value="txt").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="Word", variable=self.default_format, 
                       value="docx").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(format_frame, text="PDF", variable=self.default_format, 
                       value="pdf").pack(side=tk.LEFT)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 右对齐按钮
        ttk.Button(button_frame, text="取消", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="确定", 
                  command=self.save_config).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="重置", 
                  command=self.reset_config).pack(side=tk.RIGHT, padx=(0, 20))
    
    def browse_output_directory(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_directory.get() or os.getcwd()
        )
        
        if directory:
            self.output_directory.set(directory)
    
    def browse_log_directory(self):
        """浏览日志目录"""
        directory = filedialog.askdirectory(
            title="选择日志目录",
            initialdir=self.log_directory.get() or os.getcwd()
        )
        
        if directory:
            self.log_directory.set(directory)
    
    def validate_config(self):
        """验证配置"""
        # 验证日志目录不能为空
        if not self.log_directory.get().strip():
            messagebox.showerror("错误", "日志目录不能为空")
            return False
        
        # 验证目录路径的有效性
        output_dir = self.output_directory.get().strip()
        if output_dir and not os.path.isabs(output_dir):
            # 如果是相对路径，检查是否有效
            try:
                os.path.normpath(output_dir)
            except:
                messagebox.showerror("错误", "输出目录路径无效")
                return False
        
        log_dir = self.log_directory.get().strip()
        try:
            os.path.normpath(log_dir)
        except:
            messagebox.showerror("错误", "日志目录路径无效")
            return False
        
        return True
    
    def save_config(self):
        """保存配置"""
        if not self.validate_config():
            return
        
        try:
            # 更新配置
            self.config_manager.update({
                "output_directory": self.output_directory.get().strip(),
                "log_directory": self.log_directory.get().strip(),
                "auto_create_directories": self.auto_create_directories.get(),
                "default_format": self.default_format.get()
            })
            
            # 保存到文件
            if self.config_manager.save_config():
                # 如果设置了自动创建目录，则创建必要的目录
                if self.auto_create_directories.get():
                    self.config_manager.ensure_directories()
                
                messagebox.showinfo("成功", "配置已保存")
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "保存配置失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时发生错误: {e}")
    
    def reset_config(self):
        """重置配置"""
        result = messagebox.askyesno("确认重置", "确定要重置所有配置为默认值吗？")
        if result:
            self.output_directory.set("")
            self.log_directory.set("logs")
            self.auto_create_directories.set(True)
            self.default_format.set("txt")
    
    def cancel(self):
        """取消"""
        self.dialog.destroy()
